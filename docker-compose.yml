# 大禹故障RAG系统 Docker Compose 配置
# 支持多服务部署：ETL服务、MCP服务
version: "3.8"

services:
  # ETL服务 - 数据同步和向量化处理
  dayu-etl:
    build: .
    image: dayu-fault-rag:latest
    container_name: dayu-etl
    restart: unless-stopped
    environment:
      - SERVICE_TYPE=etl-service
      - DAYU_ENVIRONMENT=production
      - DAYU_MYSQL_HOST=${DAYU_MYSQL_HOST}
      - DAYU_MYSQL_PORT=${DAYU_MYSQL_PORT:-3306}
      - DAYU_MYSQL_USERNAME=${DAYU_MYSQL_USERNAME}
      - DAYU_MYSQL_PASSWORD=${DAYU_MYSQL_PASSWORD}
      - DAYU_MYSQL_DATABASE=${DAYU_MYSQL_DATABASE}
      - DAYU_MILVUS_HOST=${DAYU_MILVUS_HOST}
      - DAYU_MILVUS_PORT=${DAYU_MILVUS_PORT:-19530}
      - DAYU_MILVUS_COLLECTION=${DAYU_MILVUS_COLLECTION:-fault_vectors}
      - DAYU_EMBEDDING_BASE_URL=${DAYU_EMBEDDING_BASE_URL}
      - DAYU_EMBEDDING_MODEL=${DAYU_EMBEDDING_MODEL:-Qwen/Qwen3-Embedding-4B}
      - DAYU_LOG_LEVEL=${DAYU_LOG_LEVEL:-INFO}
    volumes:
      - dayu_data:/app/data
      - dayu_logs:/app/logs

  # RAG服务 - AI工具接口
  dayu-rag:
    build: .
    image: dayu-fault-rag:latest
    container_name: dayu-rag
    restart: unless-stopped
    ports:
      - "${DAYU_API_PORT:-8001}:8001"
    environment:
      - SERVICE_TYPE=mcp
      - DAYU_ENVIRONMENT=production
      - DAYU_MYSQL_HOST=${DAYU_MYSQL_HOST}
      - DAYU_MYSQL_PORT=${DAYU_MYSQL_PORT:-3306}
      - DAYU_MYSQL_USERNAME=${DAYU_MYSQL_USERNAME}
      - DAYU_MYSQL_PASSWORD=${DAYU_MYSQL_PASSWORD}
      - DAYU_MYSQL_DATABASE=${DAYU_MYSQL_DATABASE}
      - DAYU_MILVUS_HOST=${DAYU_MILVUS_HOST}
      - DAYU_MILVUS_PORT=${DAYU_MILVUS_PORT:-19530}
      - DAYU_MILVUS_COLLECTION=${DAYU_MILVUS_COLLECTION:-fault_vectors}
      - DAYU_EMBEDDING_BASE_URL=${DAYU_EMBEDDING_BASE_URL}
      - DAYU_EMBEDDING_MODEL=${DAYU_EMBEDDING_MODEL:-Qwen/Qwen3-Embedding-4B}
      - DAYU_LOG_LEVEL=${DAYU_LOG_LEVEL:-INFO}
    volumes:
      - dayu_data:/app/data
      - dayu_logs:/app/logs

# 数据卷配置
volumes:
  dayu_data:
    name: dayu_data
  dayu_logs:
    name: dayu_logs
