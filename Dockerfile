# 大禹故障RAG系统 Dockerfile
# 趣丸 Python 3.12镜像构建

# ===========================================
# 构建阶段 - 安装依赖
# ===========================================
FROM cr.ttyuyin.com/public/python:3.12 as builder

# 设置环境变量
ENV PYTHONUNBUFFERED=1 \
    PYTHONDONTWRITEBYTECODE=1 \
    PIP_NO_CACHE_DIR=1 \
    PIP_DISABLE_PIP_VERSION_CHECK=1

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    build-essential \
    curl \
    git \
    && rm -rf /var/lib/apt/lists/*

# 安装uv包管理器
RUN pip install uv

# 设置工作目录
WORKDIR /app

# 复制项目配置文件
COPY pyproject.toml uv.lock ./

# 使用uv安装依赖到虚拟环境
RUN uv venv /opt/venv
ENV PATH="/opt/venv/bin:$PATH"
RUN uv sync --frozen --no-dev

# ===========================================
# 运行阶段 - 精简镜像
# ===========================================
FROM cr.ttyuyin.com/public/python:3.12 as runtime

# 设置环境变量
ENV PYTHONUNBUFFERED=1 \
    PYTHONDONTWRITEBYTECODE=1 \
    PATH="/opt/venv/bin:$PATH" \
    DAYU_ENVIRONMENT=production

# 安装运行时系统依赖
RUN apt-get update && apt-get install -y \
    curl \
    && rm -rf /var/lib/apt/lists/* \
    && apt-get clean

# 创建非root用户
RUN groupadd -r dayu && useradd -r -g dayu dayu

# 设置工作目录
WORKDIR /app

# 从构建阶段复制虚拟环境
COPY --from=builder /opt/venv /opt/venv

# 复制应用代码
COPY --chown=dayu:dayu . .

# 创建必要的目录
RUN mkdir -p /app/data /app/logs && \
    chown -R dayu:dayu /app/data /app/logs

# 安装项目本身
RUN pip install -e .

# 切换到非root用户
USER dayu

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD python -c "import sys; sys.exit(0)" || exit 1

# 暴露端口
EXPOSE 8001

# 设置默认命令 - 通过环境变量控制服务类型
CMD ["sh", "-c", "python main.py ${SERVICE_TYPE:-mcp}"]

# ===========================================
# 构建信息
# ===========================================
LABEL maintainer="Dayu Team" \
      version="0.1.0" \
      description="大禹故障RAG系统 - 基于LangGraph的智能故障分析应用" \
      org.opencontainers.image.title="Dayu Fault RAG System" \
      org.opencontainers.image.description="RAG-based fault similarity detection system using LangGraph" \
      org.opencontainers.image.version="0.1.0" \
      org.opencontainers.image.vendor="Dayu Team" \
      org.opencontainers.image.licenses="MIT"

# ===========================================
# 使用说明
# ===========================================
# 构建镜像:
#   docker build -t dayu-fault-rag:latest .
#
# 运行MCP服务:
#   docker run -d --name dayu-mcp \
#     -p 8001:8001 \
#     --env-file .env \
#     dayu-fault-rag:latest
#
# 运行ETL服务:
#   docker run -d --name dayu-etl \
#     --env-file .env \
#     dayu-fault-rag:latest python main.py etl-service
#
# 执行一次性ETL:
#   docker run --rm \
#     --env-file .env \
#     dayu-fault-rag:latest python main.py etl full
#
# 进入容器调试:
#   docker exec -it dayu-mcp bash
#
# 查看日志:
#   docker logs -f dayu-mcp
